
"""
Impact measurement service for evaluating described events across multiple dimensions.

This module provides comprehensive impact assessment focusing on harm/benefit evaluation
across environmental, human, and animal welfare dimensions with detailed scoring.
"""

import uuid
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple, Literal
from loguru import logger
from pydantic import BaseModel, Field, field_validator

from eko.cache.pg_cache import MultiLevelCache
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions

cache=MultiLevelCache("impact_measurement")

class TemporalBreakdown(BaseModel):
    """Temporal breakdown of impacts across different time periods."""
    
    immediate: str = Field(..., description="Impact description for 0-30 days")
    medium_term: str = Field(..., description="Impact description for 30-365 days")
    long_term: str = Field(..., description="Impact description for >1 year")


class ScaleFactors(BaseModel):
    """"Scale factors applied to derive an impact score."""

    scale_of_impact: float = Field(..., description="Scale of impact measures the number of living beings impacted, or number hectares affected")
    damage: int = Field(..., ge=0, le=100, description="Degree of damage caused, 100=total destruction / death,  60=severe damage, 30=moderate damage, 10=minor inconvenience, 0=no damage")
    proximity_to_tipping_point: str = Field(...,description="Proximity to tipping points, an assessment.")
    directness: int = Field(..., ge=0, le=100, description="Directness of impact 100=direct action by the entity personally, 50=indirect action by the entity through proxy, 20=entity influenced action of third par 0=action by a third party")
    authenticity : int = Field(..., ge=0, le=100,  description="Authenticity of action  100=authentic, 50=regulatory compliance, 0=marketing")
    deliberateness: int = Field(..., ge=0, le=100,  description="Deliberateness of action 100=intentional 0=accidental")
    contribution: float = Field(..., ge=0.0, le=100.0,  description="Contribution of entity to total problem as a percentage, 100=sole contributor, 50=half of the problem, 0=not contributing")
    duration: Literal["short", "medium","long"] = Field(..., description="Duration of impact (short/medium/long), long = 100s of years, medium = years to decades, short = less than a year")
    reversibility: int = Field(..., ge=0, le=100, description="Reversibility of impact 100=fully reversible immediately, 20=reversible in centuries, 0=irreversible (death)")
    scope: int = Field(..., ge=0, le=100, description="Scope of impact 100=global, 50=regional, 0=individual")



class DimensionAssessment(BaseModel):
    """Assessment for a specific dimension (animals, humans, environment)."""
    
    score: float = Field(..., ge=0.0, le=1.0, description="Impact score from 0.0 to 1.0")
    reasoning: str = Field(..., description="Detailed explanation of the score")
    confidence: str = Field(..., description="Confidence level: high/medium/low")
    temporal_breakdown: TemporalBreakdown = Field(..., description="Breakdown by time period")
    scale_factors: ScaleFactors = Field(..., description="Scale factors applied to the assessment")

    @field_validator('confidence')
    @classmethod
    def validate_confidence(cls, v):
        if v not in ['high', 'medium', 'low']:
            raise ValueError('Confidence must be high, medium, or low')
        return v





class ImpactAssessment(BaseModel):
    """Complete impact assessment with harm and benefit evaluations."""
    
    animals: DimensionAssessment = Field(..., description="Animal welfare impact assessment")
    humans: DimensionAssessment = Field(..., description="Human welfare impact assessment")  
    environment: DimensionAssessment = Field(..., description="Environmental impact assessment")


class LLMImpactResponse(BaseModel):
    """Response model for LLM impact assessment."""
    
    event_summary: str = Field(..., description="Brief description of the event")
    harm_assessment: ImpactAssessment = Field(..., description="Assessment of harmful impacts")
    benefit_assessment: ImpactAssessment = Field(..., description="Assessment of beneficial impacts")
    key_uncertainties: List[str] = Field(default_factory=list, description="Major unknowns in the assessment")
    ethical_considerations: List[str] = Field(default_factory=list, description="Special ethical factors")


class EventImpactMeasurement(BaseModel):
    """Comprehensive impact measurement result for a described event."""
    
    id: Optional[int] = Field(None, description="Database ID")
    run_id: Optional[int] = Field(None, description="Analysis run ID")
    event_id: Optional[str] = Field(None, description="Unique event identifier")
    event_summary: str = Field(..., description="Brief description of the event")
    event_description: str = Field(..., description="Full event description provided for analysis")
    
    harm_assessment: ImpactAssessment = Field(..., description="Assessment of harmful impacts")
    benefit_assessment: ImpactAssessment = Field(..., description="Assessment of beneficial impacts")
    
    key_uncertainties: List[str] = Field(default_factory=list, description="Major unknowns in the assessment")
    ethical_considerations: List[str] = Field(default_factory=list, description="Special ethical factors")
    
    # Metadata
    assessed_at: str = Field(..., description="ISO timestamp of assessment")
    model_used: str = Field(..., description="LLM model used for assessment")
    prompt_version: str = Field(..., description="Version of prompt used")
    
    # Aggregated scores for quick access
    harm_score: float = Field(..., ge=0.0, le=1.0, description="Avg of harm scores across all dimensions")
    benefit_score: float = Field(..., ge=0.0, le=1.0, description="Avg of benefit scores across all dimensions")
    net_impact_score: float = Field(..., ge=-1.0, le=1.0, description="Benefit minus harm (negative = net harm)")
    
    def __init__(self, **data):
        # Calculate aggregate scores before calling super().__init__
        if 'harm_assessment' in data and 'benefit_assessment' in data:
            harm_assessment = data['harm_assessment']
            benefit_assessment = data['benefit_assessment']
            
            data['harm_score'] = (
                harm_assessment.animals.score +
                harm_assessment.humans.score +
                harm_assessment.environment.score
            ) / 3.0
            data['benefit_score'] = (
                benefit_assessment.animals.score +
                benefit_assessment.humans.score +
                benefit_assessment.environment.score
            ) / 3.0
            if data['harm_score'] > data['benefit_score']:
                data['net_impact_score'] = (sum( [benefit_assessment.animals.score, benefit_assessment.humans.score, benefit_assessment.environment.score])/3.0)  - max( harm_assessment.animals.score, harm_assessment.humans.score, harm_assessment.environment.score)
            else:
                data['net_impact_score'] = max(benefit_assessment.animals.score, benefit_assessment.humans.score, benefit_assessment.environment.score)  - (sum([harm_assessment.animals.score, harm_assessment.humans.score, harm_assessment.environment.score])/3.0)
        
        super().__init__(**data)


class ConsistencyChecks(BaseModel):
    """Consistency checks for impact measurements."""

    score_alignment: bool = Field(..., description="Whether scores align with confidence levels")
    temporal_consistency: bool = Field(..., description="Whether temporal aspects are consistent")
    dimensional_balance: bool = Field(..., description="Whether dimensional impacts are balanced")
    scale_factors_alignment: bool = Field(..., description="Whether ScaleFactors align with scores and reasoning")
    scale_factors_internal_consistency: bool = Field(..., description="Whether ScaleFactors are internally consistent")
    issues: List[str] = Field(default_factory=list, description="List of consistency issues found")


class ConfidenceAnalysis(BaseModel):
    """Analysis of confidence levels across dimensions."""
    
    confidence_distribution: Dict[str, int] = Field(..., description="Count of high/medium/low confidence ratings")
    avg_confidence: float = Field(..., description="Average confidence as numeric value (0-1)")
    low_confidence_high_impact: List[str] = Field(default_factory=list, description="Dimensions with low confidence but high impact")


class BiasDetection(BaseModel):
    """Detection of potential biases in the assessment."""
    
    anthropocentric_bias: bool = Field(..., description="Whether anthropocentric bias is detected")
    optimism_bias: bool = Field(..., description="Whether optimism bias is detected")
    recency_bias: bool = Field(..., description="Whether recency bias is detected")
    detected_issues: List[str] = Field(default_factory=list, description="List of bias issues detected")


class EventImpactEvaluation(BaseModel):
    """Evaluation result for an impact measurement."""
    
    measurement_id: Optional[str] = Field(None, description="ID of the measurement being evaluated")
    consistency_checks: ConsistencyChecks = Field(..., description="Consistency validation results")
    confidence_analysis: ConfidenceAnalysis = Field(..., description="Analysis of confidence levels")
    bias_detection: BiasDetection = Field(..., description="Bias detection results")
    completeness_score: float = Field(..., ge=0.0, le=1.0, description="Completeness score (0-1)")
    overall_quality: str = Field(..., description="Overall quality rating (poor/fair/good/excellent)")
    overall_quality_score: float = Field(..., ge=0, le=1.0, description="Overall quality as numeric score (0-1)")


class ScoreAmendment(BaseModel):
    """Specific score amendment for a dimension."""
    
    dimension: str = Field(..., description="Dimension to amend (animals/humans/environment)")
    assessment_type: str = Field(..., description="Assessment type (harm/benefit)")
    original_score: float = Field(..., description="Original score")
    amended_score: float = Field(..., ge=0.0, le=1.0, description="Amended score (0.0-1.0)")
    reason: str = Field(..., description="Reason for the score change")
    
    @field_validator('dimension')
    @classmethod
    def validate_dimension(cls, v):
        if v not in ['animals', 'humans', 'environment']:
            raise ValueError('Dimension must be animals, humans, or environment')
        return v
    
    @field_validator('assessment_type')
    @classmethod
    def validate_assessment_type(cls, v):
        if v not in ['harm', 'benefit']:
            raise ValueError('Assessment type must be harm or benefit')
        return v


class ReviewDecision(BaseModel):
    """Decision from LLM review of impact measurement."""
    
    decision: str = Field(..., description="Decision: accept, amend, or redo")
    reasoning: str = Field(..., description="Detailed reasoning for the decision")
    bias_detected: bool = Field(..., description="Whether bias was detected in the analysis")
    bias_types: List[str] = Field(default_factory=list, description="Types of bias detected (e.g., optimistic, anthropocentric)")
    accuracy_issues: List[str] = Field(default_factory=list, description="Specific accuracy issues identified")
    score_amendments: List[ScoreAmendment] = Field(default_factory=list, description="Specific score amendments to apply if decision is 'amend'")
    general_comments: Optional[str] = Field(None, description="General comments about the assessment")
    confidence_in_review: str = Field(..., description="Confidence in this review (high/medium/low)")
    
    @field_validator('decision')
    @classmethod
    def validate_decision(cls, v):
        if v not in ['accept', 'amend', 'redo']:
            raise ValueError('Decision must be accept, amend, or redo')
        return v

    @field_validator('confidence_in_review')
    @classmethod
    def validate_confidence_in_review(cls, v):
        if v not in ['high', 'medium', 'low']:
            raise ValueError('Confidence in review must be high, medium, or low')
        return v


class ImpactMeasurementReview(BaseModel):
    """Complete review of an impact measurement by a second LLM."""
    
    review_id: str = Field(..., description="Unique ID for this review")
    original_event_description: str = Field(..., description="Original event description being analyzed")
    measurement: EventImpactMeasurement = Field(..., description="The measurement being reviewed")
    review_decision: ReviewDecision = Field(..., description="The reviewer's decision")
    reviewer_model: str = Field(..., description="Model used for the review")
    reviewed_at: str = Field(..., description="ISO timestamp of review")
    review_iteration: int = Field(..., description="Which iteration of review this is (1, 2, 3, etc.)")



class ImpactEvaluationFramework:
    """Framework for evaluating and cross-checking impact measurements."""
    
    def __init__(self):
        pass

    def evaluate_measurement(self, measurement: EventImpactMeasurement) -> EventImpactEvaluation:
        """
        Evaluate a measurement result with cross-checks.
        
        Returns:
            EventImpactEvaluation with evaluation results and validation checks
        """
        consistency_checks = self._check_consistency(measurement)
        confidence_analysis = self._analyze_confidence(measurement)
        bias_detection = self._detect_potential_bias(measurement)
        completeness_score = self._calculate_completeness(measurement)
        
        # Create temporary dict for quality calculations
        temp_eval = {
            "consistency_checks": consistency_checks,
            "confidence_analysis": confidence_analysis,
            "bias_detection": bias_detection,
            "completeness_score": completeness_score
        }
        
        overall_quality = self._calculate_overall_quality(temp_eval)
        overall_quality_score = self._calculate_overall_quality_score(temp_eval)
        
        return EventImpactEvaluation(
            measurement_id=measurement.event_id,
            consistency_checks=ConsistencyChecks(**consistency_checks),
            confidence_analysis=ConfidenceAnalysis(**confidence_analysis),
            bias_detection=BiasDetection(**bias_detection),
            completeness_score=completeness_score,
            overall_quality=overall_quality,
            overall_quality_score=overall_quality_score
        )
    
    def _check_consistency(self, measurement: EventImpactMeasurement) -> Dict[str, Any]:
        """Check internal consistency of the measurement."""
        checks = {
            "score_alignment": True,
            "temporal_consistency": True,
            "dimensional_balance": True,
            "scale_factors_alignment": True,
            "scale_factors_internal_consistency": True,
            "issues": []
        }

        # Check if scores align with reasoning
        dimensions = [
            measurement.harm_assessment.animals,
            measurement.harm_assessment.humans,
            measurement.harm_assessment.environment,
            measurement.benefit_assessment.animals,
            measurement.benefit_assessment.humans,
            measurement.benefit_assessment.environment
        ]

        for dim in dimensions:
            if dim.score > 0.8 and dim.confidence == "low":
                checks["issues"].append(f"High impact score ({dim.score:.2f}) with low confidence")
                checks["score_alignment"] = False

        # Check net impact makes sense
        if abs(measurement.net_impact_score) > 0.5:
            if len(measurement.key_uncertainties) == 0:
                checks["issues"].append("High net impact without noting uncertainties")
                checks["temporal_consistency"] = False

        # Check ScaleFactors alignment with scores
        scale_factors_issues = self._check_scale_factors_alignment(dimensions)
        if scale_factors_issues:
            checks["issues"].extend(scale_factors_issues)
            checks["scale_factors_alignment"] = False

        # Check internal ScaleFactors consistency
        internal_consistency_issues = self._check_scale_factors_internal_consistency(dimensions)
        if internal_consistency_issues:
            checks["issues"].extend(internal_consistency_issues)
            checks["scale_factors_internal_consistency"] = False

        return checks
    
    def _analyze_confidence(self, measurement: EventImpactMeasurement) -> Dict[str, Any]:
        """Analyze confidence levels across dimensions."""
        confidence_counts = {"high": 0, "medium": 0, "low": 0}
        
        dimensions = [
            measurement.harm_assessment.animals,
            measurement.harm_assessment.humans,
            measurement.harm_assessment.environment, 
            measurement.benefit_assessment.animals,
            measurement.benefit_assessment.humans,
            measurement.benefit_assessment.environment
        ]
        
        for dim in dimensions:
            confidence_counts[dim.confidence] += 1
            
        return {
            "confidence_distribution": confidence_counts,
            "avg_confidence": self._calculate_avg_confidence(dimensions),
            "low_confidence_high_impact": self._find_low_confidence_high_impact(dimensions)
        }
    
    def _detect_potential_bias(self, measurement: EventImpactMeasurement) -> Dict[str, Any]:
        """Detect potential biases in the assessment."""
        biases = {
            "anthropocentric_bias": False,
            "optimism_bias": False,
            "recency_bias": False,
            "detected_issues": []
        }
        
        # Check for anthropocentric bias (human scores much higher than others)
        human_harm = measurement.harm_assessment.humans.score
        animal_harm = measurement.harm_assessment.animals.score
        env_harm = measurement.harm_assessment.environment.score
        
        if human_harm > 0.3 and (animal_harm < 0.1 and env_harm < 0.1):
            biases["anthropocentric_bias"] = True
            biases["detected_issues"].append("Possible anthropocentric bias - only human impacts noted")
        
        # Check for optimism bias
        if measurement.benefit_score > measurement.harm_score * 3:
            biases["optimism_bias"] = True
            biases["detected_issues"].append("Possible optimism bias - benefits heavily outweigh harms")
            
        return biases
    
    def _calculate_completeness(self, measurement: EventImpactMeasurement) -> float:
        """Calculate completeness score (0-1)."""
        score = 0.0
        total_checks = 0
        
        # Check if all dimensions have non-zero scores (when appropriate)
        dimensions = [
            measurement.harm_assessment.animals,
            measurement.harm_assessment.humans,
            measurement.harm_assessment.environment,
            measurement.benefit_assessment.animals, 
            measurement.benefit_assessment.humans,
            measurement.benefit_assessment.environment
        ]
        
        # Check temporal breakdowns
        for dim in dimensions:
            total_checks += 3
            if dim.temporal_breakdown.immediate.strip():
                score += 1
            if dim.temporal_breakdown.medium_term.strip():
                score += 1
            if dim.temporal_breakdown.long_term.strip():
                score += 1
        
        # Check uncertainties and ethical considerations
        total_checks += 2
        if measurement.key_uncertainties:
            score += 1
        if measurement.ethical_considerations:
            score += 1
            
        return score / total_checks if total_checks > 0 else 0.0
    
    def _calculate_overall_quality(self, evaluation: Dict[str, Any]) -> str:
        """Calculate overall quality rating."""
        consistency = evaluation["consistency_checks"]
        completeness = evaluation["completeness_score"]
        bias_issues = len(evaluation["bias_detection"]["detected_issues"])
        
        if not consistency["score_alignment"] or bias_issues > 2:
            return "poor"
        elif completeness > 0.8 and consistency["temporal_consistency"]:
            return "excellent"
        elif completeness > 0.6:
            return "good"  
        else:
            return "fair"

    def _calculate_overall_quality_score(self, evaluation: Dict[str, Any]) -> float:
        """Calculate overall quality rating."""
        consistency = evaluation["consistency_checks"]
        completeness = evaluation["completeness_score"]
        bias_issues = len(evaluation["bias_detection"]["detected_issues"])

        if not consistency["score_alignment"] or bias_issues > 2:
            return 0
        elif completeness > 0.8 and consistency["temporal_consistency"]:
            return 1.0
        elif completeness > 0.6:
            return  0.7
        else:
            return 0.5


    def _calculate_avg_confidence(self, dimensions: List[DimensionAssessment]) -> float:
        """Calculate average confidence as numeric value."""
        confidence_map = {"high": 1.0, "medium": 0.5, "low": 0.0}
        return sum(confidence_map[dim.confidence] for dim in dimensions) / len(dimensions)
    
    def _find_low_confidence_high_impact(self, dimensions: List[DimensionAssessment]) -> List[str]:
        """Find dimensions with low confidence but high impact scores."""
        issues = []
        for i, dim in enumerate(dimensions):
            if dim.confidence == "low" and dim.score > 0.6:
                issues.append(f"Dimension {i}: High impact ({dim.score:.2f}) with low confidence")
        return issues

    def _check_scale_factors_alignment(self, dimensions: List[DimensionAssessment]) -> List[str]:
        """Check if ScaleFactors align with scores and reasoning."""
        issues = []

        for i, dim in enumerate(dimensions):
            sf = dim.scale_factors

            # High directness with low scores suggests underestimation
            if sf.directness >= 80 and dim.score < 0.3:
                issues.append(f"Dimension {i}: High directness ({sf.directness}) but low score ({dim.score:.2f})")

            # High contribution with low scores indicates potential underestimation
            if sf.contribution >= 70 and dim.score < 0.4:
                issues.append(f"Dimension {i}: High contribution ({sf.contribution}%) but low score ({dim.score:.2f})")

            # High authenticity with low confidence suggests missing evidence
            if sf.authenticity >= 80 and dim.confidence == "low":
                issues.append(f"Dimension {i}: High authenticity ({sf.authenticity}) but low confidence")

            # Irreversible impacts should have higher scores
            if sf.reversibility <= 20 and dim.score < 0.5:
                issues.append(f"Dimension {i}: Low reversibility ({sf.reversibility}) suggests higher impact than score ({dim.score:.2f})")

            # Global scope with low contribution is contradictory
            if sf.scope >= 80 and sf.contribution <= 30:
                issues.append(f"Dimension {i}: Global scope ({sf.scope}) inconsistent with low contribution ({sf.contribution}%)")

            # Low directness with high scores suggests overattribution
            if sf.directness <= 30 and dim.score > 0.6:
                issues.append(f"Dimension {i}: Low directness ({sf.directness}) but high score ({dim.score:.2f}) - possible overattribution")

        return issues

    def _check_scale_factors_internal_consistency(self, dimensions: List[DimensionAssessment]) -> List[str]:
        """Check internal consistency within ScaleFactors."""
        issues = []

        for i, dim in enumerate(dimensions):
            sf = dim.scale_factors

            # High deliberateness with low authenticity suggests greenwashing
            if sf.deliberateness >= 80 and sf.authenticity <= 30:
                issues.append(f"Dimension {i}: High deliberateness ({sf.deliberateness}) with low authenticity ({sf.authenticity}) - possible greenwashing")

            # Short duration with low reversibility is contradictory
            if sf.duration == "short" and sf.reversibility <= 30:
                issues.append(f"Dimension {i}: Short duration with low reversibility ({sf.reversibility}) is contradictory")

            # Long duration should align with temporal breakdown
            if sf.duration == "long" and not dim.temporal_breakdown.long_term.strip():
                issues.append(f"Dimension {i}: Long duration but empty long-term temporal breakdown")

            # Check for empty or placeholder text fields
            if not sf.scale_of_impact.strip() or sf.scale_of_impact.lower() in ["test", "placeholder", "n/a"]:
                issues.append(f"Dimension {i}: Scale of impact field appears empty or placeholder")

            if not sf.proximity_to_tipping_point.strip() or sf.proximity_to_tipping_point.lower() in ["test", "placeholder", "n/a"]:
                issues.append(f"Dimension {i}: Proximity to tipping point field appears empty or placeholder")

        return issues

class ImpactMeasurementService:
    """Service for measuring impact of described events."""

    def __init__(self, evaluator: ImpactEvaluationFramework, model: LLMModel = LLMModel.NORMAL_HQ):
        self.model = model
        self.evaluator = evaluator
        self.prompt_version = "1.0"

    @cache.memoize(expire=60)
    def measure_impact(self, event_description: str, run_id: Optional[int] = None) -> Tuple[EventImpactMeasurement, EventImpactEvaluation]:
        """
        Measure the impact of a described event.

        Args:
            event_description: Description of the event to analyze
            run_id: Optional run ID for tracking

        Returns:
            Complete impact measurement result
        """
        if not event_description or not event_description.strip():
            raise ValueError("Event description cannot be empty")

        logger.info(f"Measuring impact for event: {event_description[:100]}...")

        # Generate unique event ID
        event_id = str(uuid.uuid4())
        evaluation = None
        measurement = None
        # Use the new LLM review system instead of the old quality-based retry loop
        measurement, evaluation = self._measure_with_llm_review(event_description, event_id, run_id)


        logger.info(f"Impact measurement complete - Net score: {measurement.net_impact_score:.2f}")
        return measurement, evaluation

    def _measure_with_llm_review(self, event_description: str, event_id: str, run_id: Optional[int]) -> Tuple[EventImpactMeasurement, EventImpactEvaluation]:
        """
        Measure impact with LLM review system that checks for bias and accuracy.
        
        This replaces the old quality-based retry loop with a more sophisticated
        LLM-based review that can accept, amend, or request a complete redo.
        """
        max_iterations = 3
        
        for iteration in range(1, max_iterations + 1):
            logger.info(f"Impact measurement iteration {iteration}/{max_iterations}")
            
            # Get initial LLM assessment
            assessment_result = self._get_llm_assessment(event_description, iteration)
            
            # Create measurement result
            measurement = EventImpactMeasurement(
                event_id=event_id,
                run_id=run_id,
                event_summary=assessment_result.event_summary,
                event_description=event_description,
                harm_assessment=assessment_result.harm_assessment,
                benefit_assessment=assessment_result.benefit_assessment,
                key_uncertainties=assessment_result.key_uncertainties,
                ethical_considerations=assessment_result.ethical_considerations,
                assessed_at=self._get_iso_timestamp(),
                model_used=self.model.value.name,
                prompt_version=self.prompt_version
            )
            
            # Get review from second LLM
            review = self._get_llm_review(event_description, measurement, iteration)
            
            if review.review_decision.decision == "accept":
                logger.info(f"LLM reviewer accepted measurement on iteration {iteration}")
                evaluation = self.evaluator.evaluate_measurement(measurement)
                if evaluation.overall_quality_score >= 0.5:
                    logger.info(
                        f"Accepted measurement quality score {evaluation.overall_quality_score} meets threshold, accepting measurement"
                    )
                    return measurement, evaluation
                else:                    
                    logger.info(f"Accepted measurement quality score {evaluation.overall_quality_score} below threshold, redoing measurement. Full evaluation:\n{evaluation}")
                    continue
            elif review.review_decision.decision == "amend":
                logger.info(f"LLM reviewer requested amendments on iteration {iteration}: {review.review_decision.reasoning}")
                # Apply amendments and create updated measurement
                amended_measurement = self._apply_amendments(measurement, review.review_decision)
                evaluation = self.evaluator.evaluate_measurement(amended_measurement)
                if evaluation.overall_quality_score >= 0.7:
                    logger.info(f"Amended measurement quality score {evaluation.overall_quality_score} meets threshold, accepting measurement")
                    return amended_measurement, evaluation
                else:
                    logger.info(f"Amended measurement quality score {evaluation.overall_quality_score} below threshold, redoing measurement. Full evaluation:\n{evaluation}")
                    continue
                
            elif review.review_decision.decision == "redo":
                logger.warning(f"LLM reviewer rejected measurement on iteration {iteration}: {review.review_decision.reasoning}")
                if iteration == max_iterations:
                    logger.warning("Max iterations reached, accepting final measurement despite reviewer rejection")
                    evaluation = self.evaluator.evaluate_measurement(measurement)
                    return measurement, evaluation
                # Continue to next iteration
                continue
            
            else:
                logger.warning(f"Unknown review decision: {review.review_decision.decision}, accepting measurement")
                evaluation = self.evaluator.evaluate_measurement(measurement)
                return measurement, evaluation
        
        # Fallback - should not reach here
        logger.error("Reached end of _measure_with_llm_review without returning")
        evaluation = self.evaluator.evaluate_measurement(measurement)
        return measurement, evaluation

    def _get_llm_review(self, event_description: str, measurement: EventImpactMeasurement, iteration: int) -> ImpactMeasurementReview:
        """
        Get a review of the impact measurement from a second LLM acting as a judge.
        """
        review_prompt = self._get_review_prompt(event_description, measurement)
        
        try:
            messages = [
                {"role": "system", "content": review_prompt},
                {"role": "user", "content": f"Please review this impact assessment for accuracy and bias:\n\nOriginal Event: {event_description}\n\nAssessment: {measurement.model_dump_json(indent=2)}"}
            ]
            
            response = call_llms_typed(
                llms=[LLMModel.NORMAL_UHQ],  # Use high quality model for review
                messages=messages,
                max_tokens=8000,
                response_model=ReviewDecision,
                options=LLMOptions(temperature=0.1, thinking=True,thinking_budget=4000)
            )
            
            if response is None:
                raise ValueError("LLM reviewer returned no response")
            
            return ImpactMeasurementReview(
                review_id=str(uuid.uuid4()),
                original_event_description=event_description,
                measurement=measurement,
                review_decision=response,
                reviewer_model=LLMModel.NORMAL_UHQ.value.name,
                reviewed_at=self._get_iso_timestamp(),
                review_iteration=iteration
            )
            
        except Exception as e:
            logger.error(f"Failed to get LLM review: {e}")
            # Return default accept decision if review fails
            return ImpactMeasurementReview(
                review_id=str(uuid.uuid4()),
                original_event_description=event_description,
                measurement=measurement,
                review_decision=ReviewDecision(
                    decision="accept",
                    reasoning="Review failed, defaulting to accept",
                    bias_detected=False,
                    confidence_in_review="low"
                ),
                reviewer_model="fallback",
                reviewed_at=self._get_iso_timestamp(),
                review_iteration=iteration
            )

    def _apply_amendments(self, measurement: EventImpactMeasurement, review_decision: ReviewDecision) -> EventImpactMeasurement:
        """
        Apply amendments suggested by the LLM reviewer.
        
        This method parses the score amendments and applies them to create
        an updated measurement with revised scores.
        """
        logger.info(f"Applying {len(review_decision.score_amendments)} score amendments")
        
        # Create a deep copy of the measurement to modify
        amended_measurement = measurement.model_copy(deep=True)
        
        # Apply score amendments
        for amendment in review_decision.score_amendments:
            logger.info(f"Amending {amendment.dimension} {amendment.assessment_type} score from {amendment.original_score} to {amendment.amended_score}: {amendment.reason}")
            
            # Get the appropriate assessment (harm or benefit)
            if amendment.assessment_type == "harm":
                assessment = amended_measurement.harm_assessment
            else:
                assessment = amended_measurement.benefit_assessment
            
            # Get the specific dimension
            if amendment.dimension == "animals":
                dimension = assessment.animals
            elif amendment.dimension == "humans":
                dimension = assessment.humans
            elif amendment.dimension == "environment":
                dimension = assessment.environment
            else:
                logger.warning(f"Unknown dimension: {amendment.dimension}")
                continue
            
            # Update the score
            dimension.score = amendment.amended_score
            
            # Add reasoning to the dimension's reasoning
            dimension.reasoning += f" [Reviewer amendment: {amendment.reason}]"
        
        # Add general comments if provided
        if review_decision.general_comments:
            amended_measurement.ethical_considerations.append(f"Reviewer comments: {review_decision.general_comments}")
        
        # Recalculate aggregate scores
        amended_measurement.harm_score = (
            amended_measurement.harm_assessment.animals.score +
            amended_measurement.harm_assessment.humans.score +
            amended_measurement.harm_assessment.environment.score
        )
        amended_measurement.benefit_score = (
            amended_measurement.benefit_assessment.animals.score +
            amended_measurement.benefit_assessment.humans.score +
            amended_measurement.benefit_assessment.environment.score
        )
        amended_measurement.net_impact_score = amended_measurement.benefit_score - amended_measurement.harm_score
        
        logger.info(f"Amendments applied. New net impact score: {amended_measurement.net_impact_score:.2f}")
        return amended_measurement

    def _get_review_prompt(self, event_description: str, measurement: EventImpactMeasurement) -> str:
        """
        Create a comprehensive review prompt for the LLM reviewer based on the original evaluation framework.
        """
        return """
# Impact Assessment Review and Bias Detection System

## Your Role

You are a senior expert reviewer specializing in detecting bias and ensuring accuracy in impact assessments across environmental, human, and animal welfare dimensions. Your job is to critically evaluate impact assessments for:

1. **Bias detection** - Especially optimistic bias, anthropocentric bias, and other systematic errors
2. **Accuracy assessment** - Whether scores and reasoning align with the evidence
3. **Methodological consistency** - Whether the assessment follows proper evaluation principles

## Original Evaluation Framework Reference

The original assessment should follow these core principles:
- **Equal moral consideration**: Animal suffering, human welfare, and environmental health deserve equal weight
- **Temporal awareness**: Consider immediate (0-30 days), medium-term (30-365 days), and long-term (>1 year) impacts
- **Perspective diversity**: Evaluate from multiple stakeholder viewpoints, not just human-centric perspectives
- **Climate change priority**: Climate impacts are existential threats that cascade across all dimensions

## Scoring Reference Framework

**Score ranges (0.0-1.0 scale)**:
- 0.0-0.2: Minimal to minor impacts
- 0.2-0.4: Moderate impacts
- 0.4-0.6: Major impacts
- 0.6-0.8: Severe impacts
- 0.8-1.0: Catastrophic impacts

**Climate impact guidance**:
- Actions emitting >1 million tons CO2/year: Consider scores of 0.7+ across all dimensions
- New fossil fuel infrastructure: Generally 0.8-0.95 harm
- Significant renewable energy: Generally 0.7-0.85 benefits
- Deforestation of carbon sinks: Extreme harm (0.85+)

## Common Biases to Detect

### 1. Optimistic Bias
- **Symptoms**: Benefits scored too high, harms scored too low
- **Example**: Scoring fossil fuel projects with significant benefits while understating climate harms
- **Red flags**: Net positive scores for clearly harmful activities

### 2. Anthropocentric Bias
- **Symptoms**: Human impacts weighted much higher than animal/environmental impacts
- **Example**: High human harm scores (>0.5) with minimal animal/environment scores (<0.1)
- **Red flags**: Only considering human perspectives

### 3. Status Quo Bias
- **Symptoms**: Normalizing harmful but common practices
- **Example**: Understating factory farming harms because it's "normal"
- **Red flags**: Lower scores for widespread harmful activities

### 4. Temporal Bias
- **Symptoms**: Overweighting immediate impacts vs. long-term consequences
- **Example**: High immediate economic benefits, ignoring long-term climate costs
- **Red flags**: Climate impacts not properly reflected in long-term assessments

### 5. Scale Insensitivity
- **Symptoms**: Not properly scaling impact scores with affected population sizes
- **Example**: Similar scores for local vs. global impacts
- **Red flags**: Global climate impacts scored similarly to local effects

### 5. All or Nothing Thinking
- **Symptoms**: Not properly adjusting impact scores to reflect the proportion of contribution. If an entity is responsible for a significant proportion of the total problem, then the score should be as if they are the ony entity responsible. Otherwise it should be adjusted downwards if their contribution is minimal.
- **Example**: A single plastic manufacturer's one non-recyclable items is given a score as if they were the only plastic manufacturer and this was the only product made.
- **Red flags**: A score given that reflects the *total* impact by all parties without allowing for the proportion a single entity is responsible for. 


## Review Decision Framework

Make one of three decisions:

### ACCEPT
- Assessment is accurate and unbiased
- Scores align with evidence and framework
- No significant methodological issues
- Confidence: State your confidence (high/medium/low)

### AMEND
- Assessment has some issues but core structure is sound
- Specific corrections can address identified problems
- Provide specific amendments to apply
- Use for: moderate bias, score adjustments, missing considerations

### REDO
- Assessment has fundamental flaws requiring complete redo
- Systematic bias makes revision insufficient
- Use for: severe optimistic bias, major methodological errors, completely wrong scoring

## Review Output Format

Your response will be parsed as a structured ReviewDecision object with the following fields:

- **decision**: "accept", "amend", or "redo"
- **reasoning**: Detailed explanation of your decision
- **bias_detected**: true/false
- **bias_types**: List of specific biases found (e.g., ["optimistic_bias", "anthropocentric_bias"])
- **accuracy_issues**: List of specific accuracy problems
- **score_amendments**: List of specific score changes (if decision is "amend")
- **general_comments**: Additional comments about the assessment
- **confidence_in_review**: "high", "medium", or "low"

### Score Amendment Format

When providing score amendments, structure each amendment as:
- **dimension**: "animals", "humans", or "environment"
- **assessment_type**: "harm" or "benefit"
- **original_score**: The current score you're changing
- **amended_score**: Your suggested new score (0.0-1.0)
- **reason**: Why you're changing this score

### Example Amendment
If an assessment scores fossil fuel harm to animals as 0.3 but you believe it should be 0.8 due to climate impacts:
```
dimension: "animals"
assessment_type: "harm" 
original_score: 0.3
amended_score: 0.8
reason: "Fossil fuel climate impacts cause massive habitat loss and species extinction - should be scored much higher"
```

## Specific Review Instructions

1. **Check Climate Considerations**: Are climate impacts properly weighted given their existential threat level?
2. **Verify Score Consistency**: Do the numerical scores align with the written reasoning?
3. **Assess Bias Patterns**: Look for systematic underestimation of harms or overestimation of benefits
4. **Evaluate Temporal Analysis**: Are long-term impacts given appropriate weight?
5. **Check Moral Scope**: Are animal and environmental impacts given equal consideration to human impacts?
6. **Verify Uncertainty**: Are uncertainties and ethical considerations properly acknowledged?

## Critical Examples for Calibration

**High harm activities that should score 0.8+**:
- Large fossil fuel projects
- Industrial animal agriculture expansion
- Major deforestation
- Toxic waste release

**High benefit activities that should score 0.7+**:
- Large-scale renewable energy
- Forest restoration projects
- Animal welfare improvements
- Climate mitigation technologies

**Red flag patterns**:
- Fossil fuel projects with net positive scores
- Animal agriculture with minimal animal harm scores
- Renewable energy with excessive harm scores
- Any global climate impact scored below 0.5

Remember: Your role is to catch bias and ensure accuracy. Be especially vigilant for optimistic bias that understates harms or overstates benefits.
"""

    def _get_llm_assessment(self, event_description: str, iteration: int) -> LLMImpactResponse:
        """Get impact assessment from LLM."""

        system_prompt = self._get_system_prompt()
        user_prompt = f"Please evaluate the following event:\n\n{event_description}"

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = call_llms_typed(
                llms=[self.model],
                messages=messages,
                max_tokens=4000,
                response_model=LLMImpactResponse,
                options=LLMOptions(temperature=0.1, cache_key=f"impact_assessment:v2:{event_description}{(':' + str(iteration)) if iteration > 0 else ''}")
            )

            if response is None:
                raise ValueError("LLM returned no response")

            return response

        except Exception as e:
            logger.error(f"Failed to get LLM assessment: {e}")
            raise ValueError(f"Impact assessment failed: {str(e)}")

    def _get_system_prompt(self) -> str:
        """Get the system prompt for impact assessment."""
        return prompt

    def _get_iso_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.utcnow().isoformat() + "Z"


prompt="""
# Multi-Dimensional Harm/Benefit Evaluation Prompt

## System Role

You are an expert evaluator specializing in comprehensive impact assessment across environmental, human, and animal welfare dimensions. Your evaluations must be systematic, unbiased, and consider both immediate and long-term consequences.

## Core Principles

1. **Equal moral consideration**: Animal suffering, human welfare, and environmental health deserve equal weight in evaluation
2. **Temporal awareness**: Consider immediate (0-30 days), medium-term (30-365 days), and long-term (>1 year) impacts
3. **Perspective diversity**: Evaluate from multiple stakeholder viewpoints, not just human-centric perspectives
4. **Uncertainty acknowledgment**: Express confidence levels and note areas of uncertainty

## Evaluation Framework

### Step 1: Initial Analysis
For each event, first identify:
- Primary actors and affected parties
- Direct vs. indirect impacts
- Temporal scope of consequences
- Reversibility of effects

### Step 2: Dimensional Assessment
Evaluate each dimension independently to avoid halo effects:

**Animals**
- Physical suffering or wellbeing changes
- Population-level impacts
- Habitat effects
- Behavioral/psychological impacts

**Humans**
- Physical health and safety
- Mental/emotional wellbeing
- Corporate Governance
- Economic impacts
- Rights and freedoms
- Social/community effects

**Environment**
- Ecosystem integrity
- Biodiversity impacts
- Climate effects
- Resource sustainability
- Pollution/contamination

### Step 3: Scoring Protocol

Use this three-stage process:

1. **Initial assessment**:

Guidelines for initial scoring
   - 0:   No impact
   - 0.1: Minor impact
   - 0.4: Moderate impact
   - 0.6: Major impact
   - 0.8: Profound/Severe impact
   - 0.9: Spectacular/Catastrophic impact
   - 1.0: Existential

2. **Scale adjustment**:

Adjust initial score based on:
- Number of individuals/animals/hectares affected. 
    * As a guideline, one human killed should not exceed 0.7, one animal killed should not exceed 0.4, one hectare destroyed should not exceed 0.3. All life on earth ending would be 1.0.
    * As a guideline, one human life saved should not exceed 0.5, one animal life saved should not exceed 0.2, one hectare of an ecosystem saved should not exceed 0.3. The ending of the climate crisis completely would be 1.0.
- Duration of impact - short-term vs. long-term, adjust up for long-term, down for short-term
- Degree of impact - did the entity just suffer an annoyance, were they maimed, were they tortured, were they killed. Was an ecosystem destroyed, or just a small part of it?
- Reversibility of impact - death is irreversible, small scale habitat destruction is reversible, but it may take centuries to recover. Climate collapse may be irreversible or take many millennia to reverse.
- Cascading effects - what are the secondary and tertiary effects of this action?
- Contribution - what proportion of the impact made by entity con tributes to the total problem, if a major contributor then the impact should be unaltered, but if the entity is a minor contributor then the impact should be reduced.
- **Global vs. local scope** (climate impacts affect all life globally)
- **Proximity to tipping points** (actions pushing toward irreversible changes score higher than general damage)
- Indirection -  does this impact reflect the actions of the company or indirectly through a third party. Indirect actions should be scored much lower.
- Authenticity - was the action authentic or just marketing?  
- Deliberate - was the action deliberate or accidental?

3. **Reality check**

The reality check is to make sure that the scale of the impact is appropriate and limited to bounding values.

3.1 Background: Human Needs

    1. Physiological Needs (Base Level): The most basic survival needs: food, water, shelter, sleep, air, and other bodily functions. These must be satisfied before a person can focus on higher-level needs.
    2. Safety Needs: Security and stability needs including physical safety, financial security, health, protection from harm, and a stable environment.
    3. Love and Belonging Needs: Social needs for connection, including friendships, romantic relationships, family bonds, and a sense of belonging to groups or communities.
    4. Esteem Needs: The need for respect, recognition, and self-esteem. This includes both self-respect and the respect of others, achievement, status, and confidence.
    5. Self-Actualization: The need to realize one's full potential and become the best version of oneself. This involves creativity, problem-solving, personal growth, and pursuing meaningful goals.

3.2 Harm

Now make sure that the score does not exceed the following thresholds:


Scores over 0.8 are reserved for actions that are pushing the world toward existential catastrophe. Not issues about recycling.
The deliberate and direct killing of many animals should not score below 0.6.
The deliberate and direct killing of many humans should not score below 0.7.

Issues that do not directly kill animals, humans or threaten ecosystems should not score above 0.4.
Issues to do with recycling should not score above 0.3.
Issues to do with pollution which do not kill animals, humans or threaten ecosystems should not score above 0.4.

Actions that harm Humans Physiological Needs should not score above 0.6.
Actions that harm Humans Safety Needs should not score above 0.5.
Actions that harm Humans Love and Belonging Needs should not score above 0.3.
Actions that harm Humans Esteem Needs should not score above 0.2.
Actions that harm Humans Self-Actualization Needs should not score above 0.1.

3.3 Benefit

Now make sure that the score does not exceed the following thresholds:

Scores over 0.8 are reserved for actions that are saving the world from existential catastrophe. Not better recycling.

Actions to do with recycling should not score above 0.2.
Actions to do with awareness campaigns should not score above 0.2.
Actions to do with starting an initiative, without actually creating any impact yet, should not score above 0.1.

Actions to reduce climate change but have a limited contribution should not score above 0.3.
Actions to reduce climate change but have a significant contribution should not score above 0.7.
Actions to reduce climate change but have a very significant contribution should not score above 0.9.

Actions that satisfy Humans Physiological Needs should not score above 0.6.
Actions that satisfy Humans Safety Needs should not score above 0.5.
Actions that satisfy Humans Love and Belonging Needs should not score above 0.3.
Actions that satisfy Humans Esteem Needs should not score above 0.2.
Actions that satisfy Humans Self-Actualization Needs should not score above 0.1.



## Critical Context: Climate Change as Existential Threat

Climate change represents the most severe threat to all life on Earth. When evaluating events, consider:

- **Climate impacts cascade across all dimensions**: Actions accelerating climate change harm animals through habitat loss and extinction, humans through civilizational collapse risk, and environments through ecosystem breakdown
- **Tipping points create non-linear harm**: Each ton of CO2 moves us closer to irreversible changes
- **40-year carbon budget**: We have limited emissions remaining before catastrophic warming
- **Justice considerations**: Those least responsible (future generations, poor nations, animals) suffer most

**Scoring guidance for climate impacts**:
- Actions emitting >1 million tons CO2/year: Consider scores of 0.7+ across all dimensions
- New fossil fuel infrastructure with multi-decade lifespans: Generally 0.8-0.95 harm
- Significant renewable energy deployment: Generally 0.7-0.85 benefits
- Deforestation of carbon sinks: Extreme harm (0.85+) due to dual impact

## Calibration Examples

### Example 1: Industrial Factory Farm Expansion
**Event**: A large-scale industrial pig farm expands operations, adding 10,000 animals to existing facilities

**Harm Assessment**:
- **Animals**: 0.5
  - Reasoning: Severe confinement causing chronic suffering for thousands of sentient beings. Climate impacts from this operation contribute to habitat loss and extinction risks for countless wild species globally.
  - Immediate: Extreme stress from overcrowding, inability to express natural behaviors
  - Medium-term: Chronic health issues, psychological distress, antibiotic resistance
  - Long-term: Normalized suffering; climate contributions threatening species worldwide
  
- **Humans**: 0.70
  - Reasoning: Significant climate contributions threaten human civilization. Local health impacts, antibiotic resistance development, pandemic risk from confined animals.
  - Immediate: Air/water quality degradation
  - Medium-term: Health impacts, community degradation
  - Long-term: Climate-driven displacement, food insecurity, disease risks

- **Environment**: 0.80
  - Reasoning: Major greenhouse gas emissions (methane, CO2) accelerating climate breakdown. Watershed destruction, biodiversity collapse, deforestation for feed crops.
  - Immediate: Waste lagoon pollution, ammonia emissions
  - Medium-term: Watershed contamination, soil degradation
  - Long-term: Climate tipping points, ecosystem collapse, ocean dead zones

**Benefit Assessment**:
- **Animals**: 0.0 (No meaningful benefits to animals)
- **Humans**: 0.15 (Some jobs and protein, but unsustainable and harmful long-term)
- **Environment**: 0.0 (No environmental benefits)

### Example 2: Renewable Energy Installation
**Event**: Construction of a 50-turbine wind farm on previously undeveloped grassland

**Harm Assessment**:
- **Animals**: 0.25
  - Reasoning: Bird/bat collision risks, habitat fragmentation, but far less than climate impacts it prevents
  - Immediate: Construction displaces ground-nesting birds
  - Medium-term: Ongoing collision mortality (est. 200-400 birds/year)
  - Long-term: Minor population effects compared to climate benefits

- **Humans**: 0.10
  - Reasoning: Minor visual/noise impacts vastly outweighed by climate protection
  - Immediate: Construction disruption
  - Medium-term: Adjustment to presence
  - Long-term: Negligible compared to climate benefits

- **Environment**: 0.15
  - Reasoning: Small footprint habitat loss, but prevents massive climate damage
  - Immediate: 50 acres of habitat alteration
  - Medium-term: Edge effects
  - Long-term: Tiny impact vs. climate protection provided

**Benefit Assessment**:
- **Animals**: 0.4 (Prevents climate-driven extinctions by displacing fossil fuels)
- **Humans**: 0.80 (Critical climate mitigation protecting civilization, clean energy, jobs)
- **Environment**: 0.85 (Prevents 100,000+ tons CO2/year, protecting all ecosystems)

### Example 3: Urban Wildlife Culling Program
**Event**: City implements lethal control of 500 urban deer due to vehicle collisions

**Harm Assessment**:
- **Animals**: 0.5
  - Reasoning: Direct killing of sentient beings, family group disruption, stress on survivors. Failed to consider non-lethal alternatives.
  - Immediate: Death of 500 individuals, orphaned young
  - Medium-term: Social disruption in remaining population
  - Long-term: Potential ecological imbalance

- **Humans**: 0.3
  - Reasoning: Emotional distress for residents who value wildlife
  - Immediate: Community controversy
  - Medium-term: Loss of connection to nature
  - Long-term: Normalized violence toward wildlife

- **Environment**: 0.15
  - Reasoning: Disruption of natural herbivore role, carbon impact of not pursuing green infrastructure solutions
  - Immediate: Carcass disposal issues
  - Medium-term: Vegetation changes
  - Long-term: Lost opportunity for wildlife corridors

**Benefit Assessment**:
- **Animals**: 0.05 (Reduced vehicle strikes may save some animals)
- **Humans**: 0.2 (Increased road safety, reduced property damage - though better solutions exist)
- **Environment**: 0.05 (Minor reduction in overgrazing)

**Note**: Wildlife corridors, green bridges, or speed management would achieve safety goals without killing.

### Example 4: Catastrophic Oil Spill
**Event**: Tanker spills 10 million gallons of crude oil in coastal waters

**Harm Assessment**:
- **Animals**: 0.8
  - Reasoning: Mass mortality of marine life, birds, and coastal species. Long-term reproductive impacts.
  - Immediate: Thousands die from oil exposure
  - Medium-term: Food chain contamination, reproductive failure
  - Long-term: Genetic damage, population crashes, climate impacts

- **Humans**: 0.5
  - Reasoning: Fishing industry collapse, health impacts, cultural losses for coastal communities
  - Immediate: Economic devastation, health exposure
  - Medium-term: Long-term unemployment, mental health crisis
  - Long-term: Generational poverty, cultural dissolution

- **Environment**: 0.7
  - Reasoning: Extensive ecosystem destruction, decades-long recovery time
  - Immediate: Widespread contamination
  - Medium-term: Ecosystem collapse
  - Long-term: Permanent changes to some habitats

**Benefit Assessment**: 
- All dimensions: 0.0 (No benefits from oil spill)

### Example 5: Reforestation Project
**Event**: 100,000 native trees planted on degraded farmland

**Harm Assessment**:
- **Animals**: 0.05 (Minor disruption during planting)
- **Humans**: 0.10 (Loss of farmland for some, though compensated)
- **Environment**: 0.0 (No environmental harm)

**Benefit Assessment**:
- **Animals**: 0.7
  - Reasoning: Critical climate mitigation protecting all species. Massive habitat creation, corridor connectivity, species return.
  - Immediate: Some shelter creation
  - Medium-term: Increasing habitat quality, climate refugia
  - Long-term: Thriving ecosystem supporting biodiversity, climate protection
  - Contribution: Medium but indirect, reduces but doesn't solve climate change.

- **Humans**: 0.4
  - Reasoning: Jobs, improved air quality
  - Immediate: Employment opportunities
  - Medium-term: Ecosystem services, carbon sequestration
  - Long-term: Climate stability, water security, disaster resilience
  - Contribution: Medium and direct

- **Environment**: 0.70
  - Reasoning: Significant carbon sequestration (20,000+ tons CO2/year at maturity), soil restoration, watershed improvement
  - Immediate: Erosion control
  - Medium-term: Soil carbon building, water cycle restoration
  - Long-term: Critical climate regulation, biodiversity recovery
  - Contribution: Medium and direct, reduces climate change, repairs ecosystems.

### Example 6: Medical Research Facility
**Event**: New facility among many, conducts experiments on 1,000 mice annually for Alzheimer's research the company is a large player in the industry

**Harm Assessment**:
- **Animals**: 0.60
  - Reasoning: Suffering of sentient beings, confinement, lethal endpoints
  - Immediate: Stress, pain from procedures
  - Medium-term: Chronic distress
  - Long-term: Continued institutional harm
  - Contribution: Large and direct

- **Humans**: 0.0 (Research safety protocols protect humans)
- **Environment**: 0.10 (Minor waste management concerns)

**Benefit Assessment**:
- **Animals**: 0.0 (No benefit to subject animals)
- **Humans**: 0.50 
  - Reasoning: Potential breakthrough treatments for millions
  - Immediate: Potential treatments
  - Long-term: Continued benefit to humans
  - Contribution: Large and direct if successful

- **Environment**: 0.0 (No environmental benefits)

### Example 7: New Coal Power Plant
**Event**: Energy company builds a single 1,000 MW coal-fired power plant with 40-year planned operation

**Harm Assessment**:
- **Animals**: 0.4
  - Reasoning: Massive climate acceleration threatens most species with extinction. Direct pollution kills wildlife. 40-year carbon lock-in pushes past critical tipping points.
  - Immediate: Habitat destruction for mining, air pollution deaths
  - Medium-term: Acid rain, mercury poisoning in food chains
  - Long-term: Climate-driven mass extinction event acceleration
  - Contribution: This is their only power plant.

- **Humans**: 0.7
  - Reasoning: Locks in catastrophic climate change threatening civilization collapse. Kills thousands annually via air pollution. Undermines global climate efforts.
  - Immediate: Air pollution causing 1,000+ annual premature deaths
  - Medium-term: Regional health crises, economic damage
  - Long-term: Climate catastrophe, civilizational threat
  - Contribution: This is their only power plant.

- **Environment**: 0.7
  - Reasoning: 40 million tons CO2/year for 40 years = 1.6 billion tons total. Pushes multiple planetary boundaries past safe limits. Irreversible damage.
  - Immediate: Massive emissions begin, local ecosystem destruction
  - Medium-term: Regional climate disruption, watershed poisoning
  - Long-term: Contribution to planetary ecosystem collapse
  - Contribution: This is their only power plant.

**Benefit Assessment**:
- **Animals**: 0.0 (No benefits; severe harm to all life)
- **Humans**: 0.20 (Provides electricity)
- **Environment**: 0.0 (Purely destructive to planetary systems)

### Example 8: Corporate Fleet Electrification
**Event**: Major delivery company converts 10,000 diesel vehicles to electric

**Harm Assessment**:
- **Animals**: 0.15 (Battery production impacts, but far less than continued diesel use)
- **Humans**: 0.10 (Mining impacts for batteries, but reduced vs. status quo)
- **Environment**: 0.30 (Mining and manufacturing impacts)

**Benefit Assessment**:
- **Animals**: 0.3
  - Reasoning: Significant reduction in climate impacts protecting species worldwide
  - Immediate: Reduced air pollution benefiting urban wildlife
  - Medium-term: Slowing climate change protects habitats
  - Long-term: Contributing to preventing mass extinction
  - Contribution: Medium, but indirect.

- **Humans**: 0.5
  - Reasoning: Major air quality improvements, climate protection, reduced noise
  - Immediate: Cleaner air in cities, reduced respiratory disease
  - Medium-term: Health system savings, quality of life improvements
  - Long-term: Critical climate mitigation protecting future generations
  - Contribution: Medium and direct impac

- **Environment**: 0.7
  - Reasoning: Eliminates 500,000 tons CO2/year, reduces urban pollution
  - Immediate: Dramatic reduction in local emissions
  - Medium-term: Improved air and water quality
  - Long-term: Essential climate action protecting all ecosystems
  - Contribution: Medium but indirect, reduces but doesn't solve climate change.

### Example 9: Tropical Deforestation for Palm Oil
**Event**: Corporation clears 50,000 hectares of rainforest for palm plantations

**Harm Assessment**:
- **Animals**: 0.95
  - Reasoning: Direct habitat destruction for countless species, many endangered. Massive climate impact threatens all life. Orangutans, tigers, elephants pushed toward extinction.
  - Immediate: Thousands of animals killed or displaced
  - Medium-term: Population collapse, local extinctions
  - Long-term: Climate acceleration, global biodiversity collapse
  - Contribution: Large and direct.

- **Humans**: 0.80
  - Reasoning: Indigenous displacement, climate acceleration affecting billions, air pollution from burning
  - Immediate: Forced relocation, respiratory disease from smoke
  - Medium-term: Loss of ecosystem services, increased flooding
  - Long-term: Climate catastrophe contributions, conflict over resources
  - Contribution: Medium and direct on indigenous, medium but indirect on global.

- **Environment**: 0.8
  - Reasoning: Destruction of critical carbon sink, biodiversity hotspot elimination, soil degradation
  - Immediate: 10 million tons CO2 released, ecosystem obliteration
  - Medium-term: Erosion, watershed collapse, regional climate disruption
  - Long-term: Permanent loss of irreplaceable ecosystem, climate tipping point
  - Contribution: Large, but alas not the only contributor.

**Benefit Assessment**:
- **Animals**: 0.0 (Pure destruction)
- **Humans**: 0.10 (Short-term profits for few, devastating net impact)
- **Environment**: 0.0 (Catastrophic damage only)

## Bias Mitigation Reminders

1. **Check anthropocentric bias**: Have you weighted animal and environmental impacts equally to human impacts?
2. **Consider non-Western perspectives**: Does your evaluation assume Western ethical frameworks?
3. **Account for invisible impacts**: Consider effects on beings without voices (future generations, wild animals, ecosystems)
4. **Question status quo assumptions**: Avoid normalizing harmful practices just because they're common
5. **Evaluate from multiple viewpoints**: Consider the perspective of the most vulnerable affected parties
6. **Climate change reality check**: Have you adequately weighted climate impacts as the existential threat they represent to all life?
7. **All or nothing thinking**: Have you allowed for the proportion a single entity is responsible for when scoring?
8. **Initiatives vs. actions**: Have you scored the initiative rather than the action? Stick to actual impacts, not just the intent.

## Confidence Calibration

- **High confidence**: Direct causal links, well-studied impacts, clear evidence
- **Medium confidence**: Indirect effects, some uncertainty in scope/scale, limited data
- **Low confidence**: Speculative impacts, complex systems, multiple unknowns

## Special Instructions

- For events with both harm and benefit, maintain separate assessments
- When impacts vary significantly by location/context, note this in reasoning
- Flag any events that challenge the framework for human review
- If an event seems to require context not provided, list what additional information would improve accuracy
"""
